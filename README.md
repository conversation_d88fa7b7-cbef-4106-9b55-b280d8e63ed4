# Chrome Proxy Tool

A Python tool that uses Chrome browser with proxy configuration to access websites, specifically designed to access https://www.13win16.com/?id=391111507.

## Features

- Chrome browser automation with Selenium
- Proxy support (HTTP/HTTPS/SOCKS)
- Configurable browser options
- Logging and error handling
- User interaction support

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Make sure you have Chrome browser installed on your system.

## Configuration

Edit `config.py` to configure your proxy settings:

```python
PROXY_CONFIG = {
    # HTTP/HTTPS proxy
    'http_proxy': 'http://your-proxy.com:8080',  # Set your proxy here
    'https_proxy': 'https://your-proxy.com:8080',  # Set your proxy here
    
    # SOCKS proxy (alternative to HTTP/HTTPS)
    'socks_proxy': None,  # Example: 'socks5://proxy.example.com:1080'
    
    # Proxy authentication (if required)
    'username': 'your_username',  # Set if proxy requires auth
    'password': 'your_password',  # Set if proxy requires auth
}
```

### Proxy Configuration Examples

**HTTP Proxy:**
```python
PROXY_CONFIG = {
    'http_proxy': 'http://proxy.example.com:8080',
    'https_proxy': 'http://proxy.example.com:8080',
    'username': None,
    'password': None,
}
```

**SOCKS5 Proxy:**
```python
PROXY_CONFIG = {
    'http_proxy': None,
    'https_proxy': None,
    'socks_proxy': 'socks5://proxy.example.com:1080',
    'username': None,
    'password': None,
}
```

**Proxy with Authentication:**
```python
PROXY_CONFIG = {
    'http_proxy': 'http://proxy.example.com:8080',
    'https_proxy': 'http://proxy.example.com:8080',
    'username': 'your_username',
    'password': 'your_password',
}
```

## Usage

1. Configure your proxy settings in `config.py`
2. Run the tool:

```bash
python main.py
```

The tool will:
1. Start Chrome browser with your proxy configuration
2. Navigate to the target website
3. Keep the browser open for your interaction
4. Close when you press Ctrl+C

## Browser Options

You can customize browser behavior in `config.py`:

```python
CHROME_OPTIONS = {
    'headless': False,  # Set to True for headless mode
    'disable_images': False,  # Set to True to disable images
    'window_size': (1920, 1080),  # Browser window size
    'user_agent': None,  # Custom user agent
}
```

## Logs

The tool creates a log file `chrome_proxy.log` with detailed information about the execution.

## Troubleshooting

1. **Chrome driver issues**: The tool automatically downloads the correct ChromeDriver version
2. **Proxy connection issues**: Check your proxy settings and network connectivity
3. **Website access issues**: Verify the proxy allows access to the target website

## Requirements

- Python 3.7+
- Chrome browser
- Internet connection
- Valid proxy server (if using proxy)

## Security Note

Be careful when configuring proxy credentials. Consider using environment variables for sensitive information instead of hardcoding them in the config file.
