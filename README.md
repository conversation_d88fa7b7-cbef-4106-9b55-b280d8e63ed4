# Chrome Proxy Tool - GUI Version

Tool truy cập web với Chrome browser, hỗ trợ proxy miễn phí và giao diện đồ họa thân thiện. Đ<PERSON><PERSON><PERSON> thiết kế đặc biệt để truy cập https://www.13win16.com/?id=391111507.

## ✨ Tính năng chính

- 🖥️ **Giao diện đồ họa (GUI)** - Dễ sử dụng với Tkinter
- 🌐 **Proxy miễn phí** - Tự động lấy và kiểm tra proxy từ nhiều nguồn
- 🚀 **Chrome automation** - Điều khiển Chrome với Selenium
- ⚙️ **Tùy chọn linh hoạt** - Headless mode, tắt hình ảnh, custom User Agent
- 📊 **Quản lý proxy** - Danh sách proxy với trạng thái real-time
- 📝 **Logging chi tiết** - <PERSON> dõi hoạt động và lỗi
- 🔄 **Auto-retry** - Tự động thử proxy khá<PERSON> khi lỗi

## 🚀 Cài đặt và Sử dụng

### Cách 1: Khởi chạy tự động (Khuyến nghị)
```bash
python run_gui.py
```
Script sẽ tự động:
- Kiểm tra và cài đặt thư viện thiếu
- Khởi động giao diện GUI
- Tải danh sách proxy miễn phí

### Cách 2: Cài đặt thủ công
```bash
# Cài đặt thư viện
pip install -r requirements.txt

# Khởi chạy GUI
python gui_chrome_tool.py
```

### Yêu cầu hệ thống
- Python 3.7+
- Chrome browser
- Kết nối Internet

## 📖 Hướng dẫn sử dụng

### 1. Khởi động ứng dụng
- Chạy `python run_gui.py`
- Giao diện GUI sẽ mở ra

### 2. Cấu hình Proxy
- **Không proxy**: Truy cập trực tiếp
- **Proxy tự động**: Chọn từ danh sách proxy miễn phí
- **Proxy thủ công**: Nhập proxy của bạn (format: ip:port)

### 3. Tùy chọn Browser
- **Chế độ ẩn**: Chạy Chrome không hiển thị cửa sổ
- **Tắt hình ảnh**: Tăng tốc độ tải trang
- **User Agent**: Thay đổi thông tin trình duyệt

### 4. Truy cập Website
- Nhập URL (mặc định: https://www.13win16.com/?id=391111507)
- Nhấn "Khởi động Chrome"
- Nhấn "Truy cập Website"

### Proxy Configuration Examples

**HTTP Proxy:**
```python
PROXY_CONFIG = {
    'http_proxy': 'http://proxy.example.com:8080',
    'https_proxy': 'http://proxy.example.com:8080',
    'username': None,
    'password': None,
}
```

**SOCKS5 Proxy:**
```python
PROXY_CONFIG = {
    'http_proxy': None,
    'https_proxy': None,
    'socks_proxy': 'socks5://proxy.example.com:1080',
    'username': None,
    'password': None,
}
```

**Proxy with Authentication:**
```python
PROXY_CONFIG = {
    'http_proxy': 'http://proxy.example.com:8080',
    'https_proxy': 'http://proxy.example.com:8080',
    'username': 'your_username',
    'password': 'your_password',
}
```

## Usage

1. Configure your proxy settings in `config.py`
2. Run the tool:

```bash
python main.py
```

The tool will:
1. Start Chrome browser with your proxy configuration
2. Navigate to the target website
3. Keep the browser open for your interaction
4. Close when you press Ctrl+C

## Browser Options

You can customize browser behavior in `config.py`:

```python
CHROME_OPTIONS = {
    'headless': False,  # Set to True for headless mode
    'disable_images': False,  # Set to True to disable images
    'window_size': (1920, 1080),  # Browser window size
    'user_agent': None,  # Custom user agent
}
```

## Logs

The tool creates a log file `chrome_proxy.log` with detailed information about the execution.

## 🔧 Xử lý sự cố

### ❌ Lỗi khởi động Chrome
**Nguyên nhân**: ChromeDriver không tương thích
**Giải pháp**:
- Đóng Chrome đang chạy
- Khởi động lại ứng dụng
- Tool sẽ tự động tải driver phù hợp

### ❌ Proxy không hoạt động (Vấn đề phổ biến nhất)
**Triệu chứng**:
- Website không load được
- Lỗi "net::ERR_PROXY_CONNECTION_FAILED"
- Timeout khi truy cập

**Giải pháp nhanh**:
1. **Nhấn "🔍 Test Kết nối"** để kiểm tra
2. **Thử proxy khác** trong danh sách (chọn proxy "Fast")
3. **Nhấn "Làm mới Proxy"** để tải proxy mới
4. **Chuyển sang "Không proxy"** nếu tất cả proxy đều lỗi

**Giải pháp chi tiết**: Xem file `PROXY_TROUBLESHOOTING.md`

### ❌ Website không tải được
**Nguyên nhân**: Proxy chậm hoặc bị chặn
**Giải pháp**:
- Sử dụng tính năng "🔍 Test Kết nối"
- Thử proxy có tốc độ < 5 giây
- Bật "Tắt hình ảnh" để tăng tốc
- Tool tự động fallback về "Không proxy"

### 🆘 Hỗ trợ nâng cao
- **File log**: `chrome_proxy_gui.log` chứa thông tin chi tiết
- **Hướng dẫn proxy**: `PROXY_TROUBLESHOOTING.md`
- **Test kết nối**: Sử dụng nút "🔍 Test Kết nối" trong app

## Requirements

- Python 3.7+
- Chrome browser
- Internet connection
- Valid proxy server (if using proxy)

## Security Note

Be careful when configuring proxy credentials. Consider using environment variables for sensitive information instead of hardcoding them in the config file.
