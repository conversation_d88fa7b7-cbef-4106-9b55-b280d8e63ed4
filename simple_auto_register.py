#!/usr/bin/env python3
"""
Simple Auto Register Tool
Tool đơn giản tự động đăng ký tài khoản
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import string
import logging
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from simple_chrome_tool import download_chromedriver_137

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('auto_register.log', encoding='utf-8', errors='ignore')]
)
logger = logging.getLogger(__name__)

class SimpleAutoRegister:
    def __init__(self, root):
        self.root = root
        self.root.title("🤖 Auto Register Tool - 13win")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # Variables
        self.driver = None
        self.is_running = False
        self.accounts_list = []
        self.accounts_file = "registered_accounts.json"
        self.current_account_count = 0

        # Load existing accounts
        self.load_accounts()

        self.create_widgets()

    def load_accounts(self):
        """Load danh sách tài khoản từ file"""
        try:
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    self.accounts_list = json.load(f)
        except Exception as e:
            self.accounts_list = []

    def save_accounts(self):
        """Lưu danh sách tài khoản vào file"""
        try:
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(self.accounts_list, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Lỗi lưu file: {e}")

    def add_account(self, username, password, fullname, status="Thành công", reward_info=""):
        """Thêm tài khoản vào danh sách"""
        account = {
            "username": username,
            "password": password,
            "fullname": fullname,
            "status": status,
            "reward_info": reward_info,
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.accounts_list.append(account)
        self.save_accounts()
        self.update_accounts_display()

    def create_widgets(self):
        """Tạo giao diện với tabs"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tab 1: Registration
        self.reg_frame = ttk.Frame(notebook)
        notebook.add(self.reg_frame, text="🤖 Đăng ký tự động")

        # Tab 2: Accounts List
        self.accounts_frame = ttk.Frame(notebook)
        notebook.add(self.accounts_frame, text="📋 Danh sách tài khoản")

        self.setup_registration_tab()
        self.setup_accounts_tab()

    def setup_registration_tab(self):
        """Thiết lập tab đăng ký"""
        # Main frame
        main_frame = ttk.Frame(self.reg_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🤖 Auto Register Tool",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # URL Section
        url_frame = ttk.LabelFrame(main_frame, text="Website URL", padding="10")
        url_frame.pack(fill=tk.X, pady=(0, 20))

        self.url_var = tk.StringVar(value="https://13win16.com/?id=*********")
        ttk.Entry(url_frame, textvariable=self.url_var, width=80).pack(fill=tk.X)

        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Cài đặt đăng ký", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 20))

        # Number of accounts
        accounts_row = ttk.Frame(settings_frame)
        accounts_row.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(accounts_row, text="Số lượng tài khoản muốn tạo:").pack(side=tk.LEFT)
        self.num_accounts_var = tk.StringVar(value="1")
        num_accounts_spinbox = ttk.Spinbox(accounts_row, from_=1, to=100, width=10,
                                          textvariable=self.num_accounts_var)
        num_accounts_spinbox.pack(side=tk.LEFT, padx=(10, 0))

        # Auto claim reward option
        reward_row = ttk.Frame(settings_frame)
        reward_row.pack(fill=tk.X, pady=(10, 0))

        self.auto_claim_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(reward_row, text="🎁 Tự động nhận thưởng sau khi đăng ký",
                       variable=self.auto_claim_var).pack(side=tk.LEFT)

        # Progress bar
        self.progress_var = tk.StringVar(value="Sẵn sàng...")
        ttk.Label(accounts_row, textvariable=self.progress_var).pack(side=tk.RIGHT)

        # Account Info Section
        info_frame = ttk.LabelFrame(main_frame, text="Thông tin tài khoản (để trống = tự động)", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # Username
        ttk.Label(info_frame, text="Tài khoản (để trống = tự động):").pack(anchor=tk.W)
        self.username_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.username_var, width=30).pack(anchor=tk.W, pady=(5, 10))

        # Password
        ttk.Label(info_frame, text="Mật khẩu (để trống = tự động):").pack(anchor=tk.W)
        self.password_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.password_var, width=30, show="*").pack(anchor=tk.W, pady=(5, 10))

        # Full name
        ttk.Label(info_frame, text="Họ và tên:").pack(anchor=tk.W)
        self.fullname_var = tk.StringVar(value="TRAN HOANG AN")
        ttk.Entry(info_frame, textvariable=self.fullname_var, width=30).pack(anchor=tk.W, pady=(5, 10))

        # Note
        note_label = ttk.Label(info_frame, text="💡 Form có 4 trường: Tài khoản, Mật khẩu, Nhập lại mật khẩu, Họ tên thật",
                              foreground="gray", font=("Arial", 9))
        note_label.pack(anchor=tk.W, pady=(5, 0))

        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        # Main action button
        self.start_auto_btn = ttk.Button(button_frame, text="🚀 Bắt đầu đăng ký tự động",
                                        command=self.start_auto_registration,
                                        style="Accent.TButton")
        self.start_auto_btn.pack(pady=10)

        # Debug button
        self.debug_btn = ttk.Button(button_frame, text="🔍 Debug Form",
                                   command=self.debug_form, state=tk.DISABLED)
        self.debug_btn.pack(pady=5)

        # Close all Chrome button
        self.close_all_btn = ttk.Button(button_frame, text="🔒 Đóng tất cả Chrome",
                                       command=self.close_all_chrome)
        self.close_all_btn.pack(pady=5)

        # Status label
        self.status_label = ttk.Label(button_frame, text="Sẵn sàng bắt đầu...", foreground="blue")
        self.status_label.pack(pady=5)

        # Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # Log text with scrollbar
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_container, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_accounts_tab(self):
        """Thiết lập tab danh sách tài khoản"""
        # Main frame
        main_frame = ttk.Frame(self.accounts_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="📋 Danh sách tài khoản đã đăng ký",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Stats frame
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        self.stats_label = ttk.Label(stats_frame, text="Tổng số tài khoản: 0",
                                    font=("Arial", 12, "bold"))
        self.stats_label.pack(side=tk.LEFT)

        # Clear button
        clear_btn = ttk.Button(stats_frame, text="🗑️ Xóa tất cả",
                              command=self.clear_all_accounts)
        clear_btn.pack(side=tk.RIGHT)

        # Export button
        export_btn = ttk.Button(stats_frame, text="📤 Xuất file",
                               command=self.export_accounts)
        export_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # Accounts list frame
        list_frame = ttk.LabelFrame(main_frame, text="Danh sách tài khoản", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Create treeview for accounts
        columns = ("STT", "Tài khoản", "Mật khẩu", "Họ tên", "Trạng thái", "Thưởng", "Thời gian")
        self.accounts_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # Configure columns
        self.accounts_tree.heading("STT", text="STT")
        self.accounts_tree.heading("Tài khoản", text="Tài khoản")
        self.accounts_tree.heading("Mật khẩu", text="Mật khẩu")
        self.accounts_tree.heading("Họ tên", text="Họ tên")
        self.accounts_tree.heading("Trạng thái", text="Trạng thái")
        self.accounts_tree.heading("Thưởng", text="Thưởng")
        self.accounts_tree.heading("Thời gian", text="Thời gian")

        # Configure column widths
        self.accounts_tree.column("STT", width=50)
        self.accounts_tree.column("Tài khoản", width=120)
        self.accounts_tree.column("Mật khẩu", width=120)
        self.accounts_tree.column("Họ tên", width=150)
        self.accounts_tree.column("Trạng thái", width=100)
        self.accounts_tree.column("Thưởng", width=120)
        self.accounts_tree.column("Thời gian", width=150)

        # Scrollbar for treeview
        tree_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=tree_scrollbar.set)

        self.accounts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Update display
        self.update_accounts_display()

    def update_accounts_display(self):
        """Cập nhật hiển thị danh sách tài khoản"""
        # Clear existing items
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # Add accounts to tree
        for i, account in enumerate(self.accounts_list, 1):
            self.accounts_tree.insert("", "end", values=(
                i,
                account.get("username", ""),
                account.get("password", ""),
                account.get("fullname", ""),
                account.get("status", ""),
                account.get("reward_info", ""),
                account.get("created_time", "")
            ))

        # Update stats
        total_accounts = len(self.accounts_list)
        success_accounts = len([acc for acc in self.accounts_list if acc.get("status") == "Thành công"])
        self.stats_label.config(text=f"Tổng số tài khoản: {total_accounts} | Thành công: {success_accounts}")

    def clear_all_accounts(self):
        """Xóa tất cả tài khoản"""
        if messagebox.askyesno("Xác nhận", "Bạn có chắc muốn xóa tất cả tài khoản?"):
            self.accounts_list = []
            self.save_accounts()
            self.update_accounts_display()
            messagebox.showinfo("Thành công", "Đã xóa tất cả tài khoản!")

    def export_accounts(self):
        """Xuất danh sách tài khoản ra file txt"""
        if not self.accounts_list:
            messagebox.showwarning("Cảnh báo", "Không có tài khoản nào để xuất!")
            return

        try:
            filename = f"accounts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("DANH SÁCH TÀI KHOẢN ĐĂNG KÝ\n")
                f.write("=" * 50 + "\n\n")

                for i, account in enumerate(self.accounts_list, 1):
                    f.write(f"{i}. Tài khoản: {account.get('username', '')}\n")
                    f.write(f"   Mật khẩu: {account.get('password', '')}\n")
                    f.write(f"   Họ tên: {account.get('fullname', '')}\n")
                    f.write(f"   Trạng thái: {account.get('status', '')}\n")
                    f.write(f"   Thưởng: {account.get('reward_info', '')}\n")
                    f.write(f"   Thời gian: {account.get('created_time', '')}\n")
                    f.write("-" * 30 + "\n")

            messagebox.showinfo("Thành công", f"Đã xuất file: {filename}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể xuất file: {str(e)}")

    def log(self, message):
        """Thêm message vào log"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # Update status label with latest message
        self.status_label.config(text=message[:50] + "..." if len(message) > 50 else message)

        # Safe logging without emoji
        try:
            # Remove emoji for file logging
            safe_message = message.encode('ascii', errors='ignore').decode('ascii')
            logger.info(safe_message)
        except Exception:
            # Fallback: just log to GUI
            pass

    def generate_random_string(self, length=8):
        """Tạo chuỗi ngẫu nhiên"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    def generate_password(self):
        """Tạo mật khẩu theo cấu trúc annhan + số"""
        base_names = ['annhan', 'anhhan', 'anhan', 'hoangan', 'tranhan']
        base = random.choice(base_names)

        # Random number suffix (2-4 digits)
        suffix_length = random.choice([2, 3, 4])
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(suffix_length)])

        return base + suffix

    def start_auto_registration(self):
        """Bắt đầu quy trình đăng ký tự động (mỗi tài khoản 1 Chrome riêng)"""
        def auto_process():
            try:
                # Get number of accounts to create
                num_accounts = int(self.num_accounts_var.get())
                self.current_account_count = 0

                self.log(f"🚀 Bắt đầu đăng ký {num_accounts} tài khoản...")
                self.log(f"💡 Mỗi tài khoản sẽ sử dụng 1 Chrome riêng biệt")
                self.start_auto_btn.config(state=tk.DISABLED)
                self.progress_var.set(f"Đang xử lý... (0/{num_accounts})")

                # Register each account with separate Chrome instance
                for i in range(num_accounts):
                    self.current_account_count = i + 1
                    self.progress_var.set(f"Đang đăng ký... ({self.current_account_count}/{num_accounts})")

                    self.log(f"🤖 Đăng ký tài khoản {self.current_account_count}/{num_accounts}...")
                    self.log(f"🚀 Khởi động Chrome #{self.current_account_count}...")

                    # Step 1: Start new Chrome for this account
                    if not self.setup_chrome():
                        self.log(f"❌ Không thể khởi động Chrome cho tài khoản {self.current_account_count}")
                        self.add_account("", "", "", "Lỗi khởi động Chrome")
                        continue

                    # Step 2: Navigate to website
                    self.log(f"🌐 Truy cập website cho tài khoản {self.current_account_count}...")
                    if not self.navigate_to_website():
                        self.log(f"❌ Không thể truy cập website cho tài khoản {self.current_account_count}")
                        self.close_current_chrome()
                        self.add_account("", "", "", "Lỗi truy cập website")
                        continue

                    # Enable debug button for first account
                    if i == 0:
                        self.debug_btn.config(state=tk.NORMAL)

                    # Step 3: Register this account
                    success = self.register_single_account()

                    if success:
                        self.log(f"✅ Hoàn thành tài khoản {self.current_account_count}")
                        self.log(f"🌐 Giữ Chrome #{self.current_account_count} mở để xem kết quả")

                        # Don't close Chrome if registration was successful
                        # Wait a bit to see result
                        self.log(f"⏳ Chờ 3 giây trước khi tiếp tục...")
                        time.sleep(3)

                    else:
                        self.log(f"❌ Thất bại tài khoản {self.current_account_count}")

                        # Close Chrome only if registration failed
                        self.log(f"🔒 Đóng Chrome #{self.current_account_count} (do thất bại)")
                        self.close_current_chrome()

                    # Wait between accounts
                    if i < num_accounts - 1:
                        self.log("⏳ Chờ 2 giây trước khi đăng ký tài khoản tiếp theo...")
                        time.sleep(2)

                self.progress_var.set(f"Hoàn thành! ({num_accounts}/{num_accounts})")
                self.log(f"🎉 Hoàn thành đăng ký {num_accounts} tài khoản!")

                # Show summary
                success_count = len([acc for acc in self.accounts_list[-num_accounts:]
                                   if acc.get("status") == "Thành công"])

                messagebox.showinfo(
                    "Hoàn thành",
                    f"Đã hoàn thành đăng ký!\n\n"
                    f"Tổng số tài khoản: {num_accounts}\n"
                    f"Thành công: {success_count}\n"
                    f"Thất bại: {num_accounts - success_count}\n\n"
                    f"Xem chi tiết trong tab 'Danh sách tài khoản'"
                )

            except Exception as e:
                self.log(f"❌ Lỗi quy trình: {str(e)}")
                messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
            finally:
                # Don't close Chrome instances automatically
                # Let user manually close them to see results
                self.log("💡 Các Chrome đã đăng ký thành công sẽ được giữ mở")
                self.log("💡 Bạn có thể đóng thủ công khi đã kiểm tra kết quả")
                self.start_auto_btn.config(state=tk.NORMAL)
                self.progress_var.set("Sẵn sàng...")

        threading.Thread(target=auto_process, daemon=True).start()

    def close_current_chrome(self):
        """Đóng Chrome hiện tại"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.is_running = False
                self.log("🔒 Đã đóng Chrome")
        except Exception as e:
            self.log(f"⚠️ Lỗi đóng Chrome: {str(e)}")
            self.driver = None
            self.is_running = False

    def close_all_chrome(self):
        """Đóng tất cả Chrome instances"""
        try:
            import psutil
            import subprocess

            # Method 1: Close current driver if exists
            if self.driver:
                self.close_current_chrome()

            # Method 2: Kill all Chrome processes started by this tool
            chrome_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        # Check if this Chrome was started with our temp profile
                        if any('chrome_profile_' in arg for arg in cmdline):
                            chrome_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if chrome_processes:
                self.log(f"🔍 Tìm thấy {len(chrome_processes)} Chrome processes")
                for proc in chrome_processes:
                    try:
                        proc.terminate()
                        self.log(f"🔒 Đã đóng Chrome process PID: {proc.pid}")
                    except Exception as e:
                        self.log(f"⚠️ Không thể đóng process {proc.pid}: {str(e)}")

                # Wait a bit then force kill if needed
                time.sleep(2)
                for proc in chrome_processes:
                    try:
                        if proc.is_running():
                            proc.kill()
                            self.log(f"💀 Force kill Chrome process PID: {proc.pid}")
                    except Exception:
                        pass

                self.log("✅ Đã đóng tất cả Chrome instances")
            else:
                self.log("💡 Không tìm thấy Chrome nào để đóng")

        except ImportError:
            # Fallback method without psutil
            self.log("⚠️ Không có psutil, sử dụng phương pháp thay thế...")
            try:
                import subprocess
                # Kill all chrome processes (Windows)
                subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                             capture_output=True, text=True)
                self.log("✅ Đã đóng tất cả Chrome (taskkill)")
            except Exception as e:
                self.log(f"❌ Không thể đóng Chrome: {str(e)}")
                self.log("💡 Vui lòng đóng Chrome thủ công")
        except Exception as e:
            self.log(f"❌ Lỗi đóng tất cả Chrome: {str(e)}")
            self.log("💡 Vui lòng đóng Chrome thủ công")

    def setup_chrome(self):
        """Thiết lập Chrome mới"""
        try:
            # Close existing Chrome if any
            self.close_current_chrome()

            # Setup Chrome options with unique user data directory
            chrome_options = Options()
            chrome_options.add_argument("--window-size=1200,800")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Create unique user data directory for each Chrome instance
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix=f"chrome_profile_{self.current_account_count}_")
            chrome_options.add_argument(f"--user-data-dir={temp_dir}")

            # Additional options for isolation
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--incognito")  # Use incognito mode

            self.log(f"🔧 Tạo Chrome profile riêng: {temp_dir}")

            # Get ChromeDriver
            driver_path = download_chromedriver_137()
            if not driver_path:
                raise Exception("Không thể tải ChromeDriver")

            # Start Chrome
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Hide automation indicators
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.is_running = True
            self.log(f"✅ Chrome #{self.current_account_count} đã khởi động thành công!")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi khởi động Chrome #{self.current_account_count}: {str(e)}")
            return False

    def navigate_to_website(self):
        """Truy cập website"""
        try:
            url = self.url_var.get().strip()
            if not url:
                raise Exception("URL không được để trống!")

            # Try multiple URLs
            urls_to_try = [
                url,
                url.replace("13win16", "13win20"),
                url.replace("13win16", "13win"),
                url.replace(".com", ".net"),
                "https://13win.com/?id=*********",
                "https://13win20.com/?id=*********"
            ]

            for test_url in urls_to_try:
                try:
                    self.log(f"🔗 Thử truy cập: {test_url}")
                    self.driver.set_page_load_timeout(15)
                    self.driver.get(test_url)

                    # Wait for page load
                    time.sleep(3)

                    # Check if page loaded successfully
                    title = self.driver.title.lower()
                    current_url = self.driver.current_url.lower()

                    if any(keyword in title or keyword in current_url for keyword in ['13win', 'casino', 'game']):
                        self.log(f"✅ Truy cập thành công: {self.driver.current_url}")
                        return True

                except Exception as e:
                    self.log(f"❌ Lỗi truy cập {test_url}: {str(e)[:50]}...")
                    continue

            self.log("❌ Không thể truy cập website")
            return False

        except Exception as e:
            self.log(f"❌ Lỗi: {str(e)}")
            return False

    def register_single_account(self):
        """Đăng ký 1 tài khoản"""
        try:
            # Generate unique account info for each registration
            base_username = self.username_var.get().strip()
            base_password = self.password_var.get().strip()

            # Generate unique username if not provided or add suffix for multiple accounts
            if base_username:
                username = f"{base_username}{self.current_account_count}" if self.current_account_count > 1 else base_username
            else:
                username = f"user{random.randint(1000, 9999)}"

            # Generate unique password for each account
            password = base_password or self.generate_password()

            fullname = self.fullname_var.get().strip() or "TRAN HOANG AN"

            self.log(f"📝 Thông tin tài khoản {self.current_account_count}:")
            self.log(f"   👤 Tài khoản: {username}")
            self.log(f"   🔑 Mật khẩu: {password}")
            self.log(f"   👨 Họ tên: {fullname}")

            # Wait for page to be ready
            time.sleep(3)

            # Fill form and submit
            success = self.fill_registration_form(username, password, fullname)

            if success:
                # Check if auto claim reward is enabled
                reward_info = ""
                if self.auto_claim_var.get():
                    self.log("🎁 Bắt đầu tự động nhận thưởng...")
                    reward_info = self.auto_claim_reward()

                # Add to accounts list
                self.add_account(username, password, fullname, "Thành công", reward_info)
                return True
            else:
                # Add to accounts list with failed status
                self.add_account(username, password, fullname, "Thất bại")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi đăng ký: {str(e)}")
            # Add failed account to list
            self.add_account("", "", "", f"Lỗi: {str(e)[:50]}")
            return False

    def fill_registration_form(self, username, password, fullname):
        """Điền form đăng ký"""
        try:
            # Check current username field value
            try:
                username_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                current_value = username_field.get_attribute('value')
                if current_value and current_value.startswith('user'):
                    self.log(f"📱 Tài khoản hiện tại: {current_value}")
                    username = current_value  # Use existing username
                else:
                    # Clear and fill new username
                    username_field.clear()
                    username_field.send_keys(username)
                    self.log(f"✅ Đã điền tài khoản: {username}")
            except Exception as e:
                self.log(f"⚠️ Không thể kiểm tra trường tài khoản: {str(e)[:50]}")

            time.sleep(1)

            # Fill password fields - tìm và điền CẢ 2 trường mật khẩu
            password_selectors = [
                'input[type="password"]',
                'input[placeholder*="Mật khẩu"]',
                'input[placeholder*="mật khẩu"]',
                'input[name*="password"]',
                'input[id*="password"]'
            ]

            password_fields_found = []
            for selector in password_selectors:
                try:
                    fields = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for field in fields:
                        if field.is_displayed() and field.is_enabled():
                            password_fields_found.append(field)
                except Exception:
                    continue

            # Remove duplicates
            unique_password_fields = []
            for field in password_fields_found:
                if field not in unique_password_fields:
                    unique_password_fields.append(field)

            self.log(f"🔍 Tìm thấy {len(unique_password_fields)} trường mật khẩu")

            # Fill all password fields (usually 2: password and confirm password)
            password_filled_count = 0
            for i, field in enumerate(unique_password_fields):
                try:
                    field.clear()
                    field.send_keys(password)

                    field_name = "mật khẩu" if i == 0 else f"nhập lại mật khẩu ({i+1})"
                    self.log(f"✅ Đã điền {field_name}")
                    password_filled_count += 1
                    time.sleep(1)
                except Exception as e:
                    self.log(f"⚠️ Lỗi điền trường mật khẩu {i+1}: {str(e)[:30]}")

            if password_filled_count == 0:
                self.log("⚠️ Không tìm thấy trường mật khẩu nào")
                return False
            elif password_filled_count == 1:
                self.log("⚠️ Chỉ điền được 1 trường mật khẩu, có thể thiếu 'Nhập lại mật khẩu'")
            else:
                self.log(f"✅ Đã điền {password_filled_count} trường mật khẩu")

            # Fill fullname - tìm trường "Họ tên thật"
            fullname_selectors = [
                'input[placeholder*="Họ Tên Thật"]',
                'input[placeholder*="họ tên thật"]',
                'input[placeholder*="Họ tên thật"]',
                'input[placeholder*="HỌ TÊN THẬT"]',
                'input[placeholder*="Họ Tên"]',
                'input[placeholder*="họ tên"]',
                'input[name*="fullname"]',
                'input[name*="realname"]',
                'input[name*="name"]:not([name*="username"])',
                'input[id*="fullname"]',
                'input[id*="realname"]'
            ]

            fullname_filled = False
            for selector in fullname_selectors:
                try:
                    name_fields = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for field in name_fields:
                        if field.is_displayed() and field.is_enabled():
                            # Check if this field is not a password field
                            field_type = field.get_attribute('type') or 'text'
                            if field_type.lower() != 'password':
                                field.clear()
                                field.send_keys(fullname)
                                self.log(f"✅ Đã điền họ tên thật: {fullname}")
                                fullname_filled = True
                                time.sleep(1)
                                break
                    if fullname_filled:
                        break
                except Exception:
                    continue

            # Try XPath for "Họ tên thật" if CSS selectors failed
            if not fullname_filled:
                xpath_selectors = [
                    "//input[@placeholder[contains(., 'Họ Tên Thật')]]",
                    "//input[@placeholder[contains(., 'họ tên thật')]]",
                    "//input[@placeholder[contains(., 'Họ tên thật')]]",
                    "//input[@placeholder[contains(., 'HỌ TÊN THẬT')]]"
                ]

                for xpath in xpath_selectors:
                    try:
                        name_fields = self.driver.find_elements(By.XPATH, xpath)
                        for field in name_fields:
                            if field.is_displayed() and field.is_enabled():
                                field.clear()
                                field.send_keys(fullname)
                                self.log(f"✅ Đã điền họ tên thật (XPath): {fullname}")
                                fullname_filled = True
                                time.sleep(1)
                                break
                        if fullname_filled:
                            break
                    except Exception:
                        continue

            if not fullname_filled:
                self.log("⚠️ Không tìm thấy trường 'Họ tên thật'")

            # Wait before submit
            time.sleep(2)

            # Find and click submit button
            return self.click_submit_button()

        except Exception as e:
            self.log(f"❌ Lỗi điền form: {str(e)}")
            return False

    def click_submit_button(self):
        """Tìm và click nút submit"""
        try:
            self.log("🔍 Đang tìm nút đăng ký...")

            # Wait a bit more for any dynamic content
            time.sleep(2)

            # First, let's find ALL clickable elements and analyze them
            self.log("📋 Phân tích tất cả các nút trên trang...")

            all_buttons = []
            try:
                # Find all buttons
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit'], input[type='button']")
                divs_clickable = self.driver.find_elements(By.CSS_SELECTOR, "div[onclick], div[role='button']")

                all_buttons = buttons + inputs + divs_clickable
                self.log(f"📊 Tìm thấy {len(all_buttons)} phần tử có thể click")

                # Analyze each button
                for i, element in enumerate(all_buttons):
                    try:
                        if element.is_displayed() and element.is_enabled():
                            element_text = element.text.strip()
                            element_value = element.get_attribute('value') or ''
                            element_class = element.get_attribute('class') or ''
                            element_id = element.get_attribute('id') or ''
                            element_onclick = element.get_attribute('onclick') or ''

                            self.log(f"  Nút {i+1}: Text='{element_text}' Value='{element_value}' Class='{element_class}' ID='{element_id}'")

                            # Check if this looks like a register button
                            text_keywords = ['đăng ký', 'đăng kí', 'register', 'submit', 'đăng']
                            class_keywords = ['submit', 'register', 'btn-primary', 'btn-submit']

                            is_register_button = (
                                any(keyword in element_text.lower() for keyword in text_keywords) or
                                any(keyword in element_value.lower() for keyword in text_keywords) or
                                any(keyword in element_class.lower() for keyword in class_keywords) or
                                any(keyword in element_id.lower() for keyword in class_keywords) or
                                'submit' in element_onclick.lower()
                            )

                            if is_register_button:
                                self.log(f"🎯 Nút {i+1} có vẻ là nút đăng ký!")

                                # Try to click this button
                                success = self.try_click_element(element, f"Nút {i+1}")
                                if success:
                                    # Wait and check if page changed or form submitted
                                    time.sleep(3)

                                    # Check for success indicators
                                    current_url = self.driver.current_url
                                    page_source = self.driver.page_source.lower()

                                    success_indicators = [
                                        'thành công', 'success', 'đăng ký thành công',
                                        'registration successful', 'welcome', 'chào mừng'
                                    ]

                                    error_indicators = [
                                        'lỗi', 'error', 'failed', 'thất bại', 'không hợp lệ'
                                    ]

                                    if any(indicator in page_source for indicator in success_indicators):
                                        self.log("✅ Đăng ký thành công! (Phát hiện thông báo thành công)")
                                        return True
                                    elif any(indicator in page_source for indicator in error_indicators):
                                        self.log("⚠️ Có lỗi trong quá trình đăng ký")
                                        return False
                                    elif 'login' in current_url.lower() or 'đăng nhập' in page_source:
                                        self.log("✅ Đăng ký thành công! (Chuyển đến trang đăng nhập)")
                                        return True
                                    else:
                                        self.log("✅ Đã click nút đăng ký (không rõ kết quả)")
                                        return True
                    except Exception as e:
                        continue

            except Exception as e:
                self.log(f"❌ Lỗi phân tích nút: {str(e)}")

            # If no obvious register button found, try common selectors
            self.log("🔍 Thử các selector phổ biến...")

            common_selectors = [
                "//button[contains(text(), 'ĐĂNG KÝ')]",
                "//button[contains(text(), 'Đăng ký')]",
                "//input[@value='ĐĂNG KÝ']",
                "//input[@value='Đăng ký']",
                "button[type='submit']",
                "input[type='submit']",
                ".btn-primary",
                ".btn-submit",
                "button[style*='orange']",
                "button[style*='background']"
            ]

            for selector in common_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            self.log(f"🎯 Thử selector: {selector}")
                            success = self.try_click_element(element, f"Selector: {selector}")
                            if success:
                                time.sleep(3)
                                self.log("✅ Đã click nút đăng ký!")
                                return True
                except:
                    continue

            self.log("⚠️ Không tìm thấy nút đăng ký hoặc không thể click")
            self.log("💡 Vui lòng kiểm tra trang web và click nút đăng ký thủ công")
            return False

        except Exception as e:
            self.log(f"❌ Lỗi tìm nút submit: {str(e)}")
            return False

    def try_click_element(self, element, description):
        """Thử click một element với nhiều phương pháp"""
        try:
            # Scroll to element
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
            time.sleep(1)

            # Highlight element for debugging
            self.driver.execute_script("arguments[0].style.border='3px solid red';", element)
            time.sleep(0.5)

            # Method 1: Normal click
            try:
                element.click()
                self.log(f"✅ Click thành công ({description}) - Normal click")
                return True
            except Exception as e1:
                self.log(f"⚠️ Normal click thất bại: {str(e1)[:50]}")

            # Method 2: JavaScript click
            try:
                self.driver.execute_script("arguments[0].click();", element)
                self.log(f"✅ Click thành công ({description}) - JavaScript click")
                return True
            except Exception as e2:
                self.log(f"⚠️ JavaScript click thất bại: {str(e2)[:50]}")

            # Method 3: Submit if it's a form
            try:
                if element.tag_name.lower() in ['input', 'button']:
                    form = element.find_element(By.XPATH, "./ancestor::form[1]")
                    if form:
                        form.submit()
                        self.log(f"✅ Submit form thành công ({description})")
                        return True
            except Exception as e3:
                self.log(f"⚠️ Form submit thất bại: {str(e3)[:50]}")

            # Method 4: Send ENTER key
            try:
                element.send_keys("\n")
                self.log(f"✅ Send ENTER thành công ({description})")
                return True
            except Exception as e4:
                self.log(f"⚠️ Send ENTER thất bại: {str(e4)[:50]}")

            return False

        except Exception as e:
            self.log(f"❌ Không thể click element ({description}): {str(e)}")
            return False

    def auto_claim_reward(self):
        """Tự động nhận thưởng sau khi đăng ký thành công"""
        try:
            # Wait for registration to complete
            time.sleep(3)

            # Navigate to reward page
            reward_url = "https://13win16.com/home/<USER>"
            self.log(f"🌐 Truy cập trang nhiệm vụ: {reward_url}")

            try:
                self.driver.get(reward_url)
                time.sleep(5)  # Wait for page to load
            except Exception as e:
                self.log(f"❌ Không thể truy cập trang nhiệm vụ: {str(e)}")
                return "Lỗi truy cập trang thưởng"

            # Look for registration reward task
            self.log("🔍 Tìm kiếm nhiệm vụ đăng ký tài khoản...")

            # Find the registration task and reward amount
            reward_amount = self.find_registration_reward()
            if not reward_amount:
                self.log("⚠️ Không tìm thấy nhiệm vụ đăng ký tài khoản")
                return "Không tìm thấy nhiệm vụ"

            # Try to click the green "Nhận" button
            success = self.click_claim_button()
            if success:
                self.log(f"✅ Đã nhận thưởng thành công!")
                return f"DKTK({reward_amount})"
            else:
                self.log("❌ Không thể nhận thưởng")
                return f"Có thưởng {reward_amount} (chưa nhận)"

        except Exception as e:
            self.log(f"❌ Lỗi tự động nhận thưởng: {str(e)}")
            return f"Lỗi: {str(e)[:30]}"

    def find_registration_reward(self):
        """Tìm nhiệm vụ đăng ký tài khoản và số tiền thưởng"""
        try:
            # Look for text containing "Đăng ký tài khoản"
            registration_keywords = [
                "đăng ký tài khoản",
                "đăng kí tài khoản",
                "registration",
                "register account"
            ]

            # Find elements containing registration keywords
            page_source = self.driver.page_source.lower()

            for keyword in registration_keywords:
                if keyword in page_source:
                    self.log(f"✅ Tìm thấy nhiệm vụ: {keyword}")

                    # Look for reward amount (usually 13.00, 14.00, etc.)
                    import re

                    # Pattern to find reward amounts
                    amount_patterns = [
                        r'(\d+\.00)',  # 13.00, 14.00
                        r'(\d+)k',     # 13k, 14k
                        r'(\d+),(\d+)', # 13,00
                    ]

                    for pattern in amount_patterns:
                        matches = re.findall(pattern, page_source)
                        if matches:
                            # Look for amounts around 13-15 (typical registration reward)
                            for match in matches:
                                if isinstance(match, tuple):
                                    amount = match[0]
                                else:
                                    amount = match

                                try:
                                    amount_num = float(amount)
                                    if 10 <= amount_num <= 20:  # Reasonable reward range
                                        self.log(f"💰 Phát hiện thưởng: {amount_num}k")
                                        return f"{amount_num}k"
                                except:
                                    continue

                    # Default if found keyword but no specific amount
                    return "14k"

            self.log("⚠️ Không tìm thấy nhiệm vụ đăng ký")
            return None

        except Exception as e:
            self.log(f"❌ Lỗi tìm thưởng: {str(e)}")
            return None

    def click_claim_button(self):
        """Tìm và click nút nhận thưởng màu xanh"""
        try:
            self.log("🔍 Tìm nút nhận thưởng...")

            # Wait for page to be fully loaded
            time.sleep(3)

            # Look for green "Nhận" button
            claim_selectors = [
                # Text-based selectors
                "//button[contains(text(), 'Nhận')]",
                "//button[contains(text(), 'NHẬN')]",
                "//button[contains(text(), 'nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",

                # Class-based selectors for green buttons
                "button[style*='green']",
                "button[style*='#00']", # Green color codes
                "button[class*='green']",
                "button[class*='success']",
                ".btn-success",
                ".btn-green",

                # Generic button selectors
                "button[type='button']",
                "button[type='submit']"
            ]

            for selector in claim_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # Check if this looks like a claim button
                            element_text = element.text.strip().lower()
                            element_style = element.get_attribute('style') or ''
                            element_class = element.get_attribute('class') or ''

                            # Check for green color or "Nhận" text
                            is_claim_button = (
                                'nhận' in element_text or
                                'green' in element_style.lower() or
                                'green' in element_class.lower() or
                                '#00' in element_style  # Green color codes
                            )

                            if is_claim_button:
                                self.log(f"🎯 Tìm thấy nút nhận: '{element_text}' - {selector}")

                                # Try to click
                                success = self.try_click_element(element, f"Nút nhận thưởng")
                                if success:
                                    # Wait and check for success
                                    time.sleep(3)

                                    # Check if reward was claimed
                                    page_source = self.driver.page_source.lower()
                                    success_indicators = [
                                        'thành công', 'success', 'đã nhận', 'claimed',
                                        'hoàn thành', 'completed'
                                    ]

                                    if any(indicator in page_source for indicator in success_indicators):
                                        self.log("✅ Nhận thưởng thành công!")
                                        return True
                                    else:
                                        self.log("✅ Đã click nút nhận (chờ xác nhận)")
                                        return True

                except Exception as e:
                    continue

            self.log("⚠️ Không tìm thấy nút nhận thưởng")
            return False

        except Exception as e:
            self.log(f"❌ Lỗi click nút nhận: {str(e)}")
            return False

    def start_chrome(self):
        """Bước 1: Khởi động Chrome"""
        def start():
            try:
                self.log("🚀 Đang khởi động Chrome...")
                self.start_btn.config(state=tk.DISABLED)

                # Setup Chrome options
                chrome_options = Options()
                chrome_options.add_argument("--window-size=1200,800")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # Get ChromeDriver
                driver_path = download_chromedriver_137()
                if not driver_path:
                    raise Exception("Không thể tải ChromeDriver")

                # Start Chrome
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # Hide automation indicators
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                self.is_running = True
                self.log("✅ Chrome đã khởi động thành công!")

                # Enable next button
                self.navigate_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log(f"❌ Lỗi khởi động Chrome: {str(e)}")
                self.start_btn.config(state=tk.NORMAL)

        threading.Thread(target=start, daemon=True).start()

    def navigate_website(self):
        """Bước 2: Truy cập website"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        def navigate():
            try:
                self.log("🌐 Đang truy cập website...")
                self.navigate_btn.config(state=tk.DISABLED)

                url = self.url_var.get().strip()
                if not url:
                    raise Exception("URL không được để trống!")

                # Try multiple URLs
                urls_to_try = [
                    url,
                    url.replace("13win16", "13win20"),
                    url.replace("13win16", "13win"),
                    url.replace(".com", ".net"),
                    "https://13win.com/?id=*********",
                    "https://13win20.com/?id=*********"
                ]

                success = False
                for test_url in urls_to_try:
                    try:
                        self.log(f"🔗 Thử truy cập: {test_url}")
                        self.driver.set_page_load_timeout(15)
                        self.driver.get(test_url)

                        # Wait for page load
                        time.sleep(3)

                        # Check if page loaded successfully
                        title = self.driver.title.lower()
                        current_url = self.driver.current_url.lower()

                        if any(keyword in title or keyword in current_url for keyword in ['13win', 'casino', 'game']):
                            self.log(f"✅ Truy cập thành công: {self.driver.current_url}")
                            self.log(f"📄 Tiêu đề: {self.driver.title}")
                            success = True
                            break

                    except Exception as e:
                        self.log(f"❌ Lỗi truy cập {test_url}: {str(e)[:50]}...")
                        continue

                if success:
                    # Enable register button and debug button
                    self.register_btn.config(state=tk.NORMAL)
                    self.debug_btn.config(state=tk.NORMAL)
                    self.log("✅ Sẵn sàng để đăng ký!")
                else:
                    self.log("❌ Không thể truy cập website")
                    self.navigate_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log(f"❌ Lỗi: {str(e)}")
                self.navigate_btn.config(state=tk.NORMAL)

        threading.Thread(target=navigate, daemon=True).start()

    def debug_form(self):
        """Debug: Hiển thị tất cả form elements trên trang"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        def debug():
            try:
                self.log("🔍 Đang phân tích form...")

                # Find all input elements
                inputs = self.driver.find_elements(By.TAG_NAME, "input")
                self.log(f"📋 Tìm thấy {len(inputs)} input elements:")

                for i, input_elem in enumerate(inputs):
                    try:
                        if input_elem.is_displayed():
                            input_type = input_elem.get_attribute('type') or 'text'
                            input_name = input_elem.get_attribute('name') or 'N/A'
                            input_id = input_elem.get_attribute('id') or 'N/A'
                            input_placeholder = input_elem.get_attribute('placeholder') or 'N/A'
                            input_value = input_elem.get_attribute('value') or 'Empty'

                            self.log(f"  {i+1}. Type: {input_type} | Name: {input_name} | ID: {input_id}")
                            self.log(f"      Placeholder: {input_placeholder} | Value: {input_value}")
                    except:
                        continue

                # Find all buttons
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                self.log(f"🔘 Tìm thấy {len(buttons)} button elements:")

                for i, button in enumerate(buttons):
                    try:
                        if button.is_displayed():
                            button_text = button.text or 'No text'
                            button_type = button.get_attribute('type') or 'button'
                            button_class = button.get_attribute('class') or 'N/A'

                            self.log(f"  {i+1}. Text: '{button_text}' | Type: {button_type} | Class: {button_class}")
                    except:
                        continue

                # Find all select elements
                selects = self.driver.find_elements(By.TAG_NAME, "select")
                if selects:
                    self.log(f"📝 Tìm thấy {len(selects)} select elements:")
                    for i, select in enumerate(selects):
                        try:
                            if select.is_displayed():
                                select_name = select.get_attribute('name') or 'N/A'
                                select_id = select.get_attribute('id') or 'N/A'
                                self.log(f"  {i+1}. Name: {select_name} | ID: {select_id}")
                        except:
                            continue

                self.log("✅ Debug hoàn thành!")

            except Exception as e:
                self.log(f"❌ Lỗi debug: {str(e)}")

        threading.Thread(target=debug, daemon=True).start()

    def find_and_fill_field(self, field_selectors, value, field_name, clear_first=True):
        """Tìm và điền field"""
        for selector in field_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        if clear_first:
                            element.clear()
                        element.send_keys(value)
                        self.log(f"✅ Đã điền {field_name}: {value}")
                        time.sleep(0.5)
                        return True
            except Exception:
                continue

        # Try XPath selectors
        xpath_selectors = [
            f"//input[@placeholder[contains(., '{field_name.lower()}')]]",
            f"//input[@name[contains(., '{field_name.lower()}')]]",
            f"//input[@id[contains(., '{field_name.lower()}')]]"
        ]

        for xpath in xpath_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        if clear_first:
                            element.clear()
                        element.send_keys(value)
                        self.log(f"✅ Đã điền {field_name}: {value}")
                        time.sleep(0.5)
                        return True
            except Exception:
                continue

        self.log(f"⚠️ Không tìm thấy trường {field_name}")
        return False

    def auto_register(self):
        """Bước 3: Tự động đăng ký"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Vui lòng hoàn thành bước 1 và 2!")
            return

        def register():
            try:
                self.log("🤖 Bắt đầu tự động đăng ký...")
                self.register_btn.config(state=tk.DISABLED)

                # Generate account info
                username = self.username_var.get().strip() or f"user{random.randint(1000, 9999)}"
                password = self.password_var.get().strip() or self.generate_password()
                fullname = self.fullname_var.get().strip() or "TRAN HOANG AN"

                self.log(f"📝 Thông tin đăng ký:")
                self.log(f"   👤 Tài khoản: {username}")
                self.log(f"   🔑 Mật khẩu: {password}")
                self.log(f"   👨 Họ tên: {fullname}")

                # Wait a bit for page to be ready
                time.sleep(3)

                # Check current username field value
                try:
                    username_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    current_value = username_field.get_attribute('value')
                    if current_value and current_value.startswith('user'):
                        self.log(f"📱 Tài khoản hiện tại: {current_value}")
                        username = current_value  # Use existing username
                    else:
                        # Clear and fill new username
                        username_field.clear()
                        username_field.send_keys(username)
                        self.log(f"✅ Đã điền tài khoản: {username}")
                except Exception as e:
                    self.log(f"⚠️ Không thể kiểm tra trường tài khoản: {str(e)[:50]}")

                time.sleep(1)

                # Fill password fields - tìm và điền CẢ 2 trường mật khẩu
                password_selectors = [
                    'input[type="password"]',
                    'input[placeholder*="Mật khẩu"]',
                    'input[placeholder*="mật khẩu"]',
                    'input[name*="password"]',
                    'input[id*="password"]'
                ]

                password_fields_found = []
                for selector in password_selectors:
                    try:
                        fields = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for field in fields:
                            if field.is_displayed() and field.is_enabled():
                                password_fields_found.append(field)
                    except Exception:
                        continue

                # Remove duplicates
                unique_password_fields = []
                for field in password_fields_found:
                    if field not in unique_password_fields:
                        unique_password_fields.append(field)

                self.log(f"🔍 Tìm thấy {len(unique_password_fields)} trường mật khẩu")

                # Fill all password fields (usually 2: password and confirm password)
                password_filled_count = 0
                for i, field in enumerate(unique_password_fields):
                    try:
                        field.clear()
                        field.send_keys(password)

                        field_name = "mật khẩu" if i == 0 else f"nhập lại mật khẩu ({i+1})"
                        self.log(f"✅ Đã điền {field_name}")
                        password_filled_count += 1
                        time.sleep(1)
                    except Exception as e:
                        self.log(f"⚠️ Lỗi điền trường mật khẩu {i+1}: {str(e)[:30]}")

                if password_filled_count == 0:
                    self.log("⚠️ Không tìm thấy trường mật khẩu nào")
                elif password_filled_count == 1:
                    self.log("⚠️ Chỉ điền được 1 trường mật khẩu, có thể thiếu 'Nhập lại mật khẩu'")
                else:
                    self.log(f"✅ Đã điền {password_filled_count} trường mật khẩu")

                # Fill fullname - tìm trường "Họ tên thật"
                fullname_selectors = [
                    'input[placeholder*="Họ Tên Thật"]',
                    'input[placeholder*="họ tên thật"]',
                    'input[placeholder*="Họ tên thật"]',
                    'input[placeholder*="HỌ TÊN THẬT"]',
                    'input[placeholder*="Họ Tên"]',
                    'input[placeholder*="họ tên"]',
                    'input[name*="fullname"]',
                    'input[name*="realname"]',
                    'input[name*="name"]:not([name*="username"])',
                    'input[id*="fullname"]',
                    'input[id*="realname"]'
                ]

                fullname_filled = False
                for selector in fullname_selectors:
                    try:
                        name_fields = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for field in name_fields:
                            if field.is_displayed() and field.is_enabled():
                                # Check if this field is not a password field
                                field_type = field.get_attribute('type') or 'text'
                                if field_type.lower() != 'password':
                                    field.clear()
                                    field.send_keys(fullname)
                                    self.log(f"✅ Đã điền họ tên thật: {fullname}")
                                    fullname_filled = True
                                    time.sleep(1)
                                    break
                        if fullname_filled:
                            break
                    except Exception:
                        continue

                # Try XPath for "Họ tên thật" if CSS selectors failed
                if not fullname_filled:
                    xpath_selectors = [
                        "//input[@placeholder[contains(., 'Họ Tên Thật')]]",
                        "//input[@placeholder[contains(., 'họ tên thật')]]",
                        "//input[@placeholder[contains(., 'Họ tên thật')]]",
                        "//input[@placeholder[contains(., 'HỌ TÊN THẬT')]]"
                    ]

                    for xpath in xpath_selectors:
                        try:
                            name_fields = self.driver.find_elements(By.XPATH, xpath)
                            for field in name_fields:
                                if field.is_displayed() and field.is_enabled():
                                    field.clear()
                                    field.send_keys(fullname)
                                    self.log(f"✅ Đã điền họ tên thật (XPath): {fullname}")
                                    fullname_filled = True
                                    time.sleep(1)
                                    break
                            if fullname_filled:
                                break
                        except Exception:
                            continue

                if not fullname_filled:
                    self.log("⚠️ Không tìm thấy trường 'Họ tên thật'")

                # Wait before submit
                time.sleep(2)

                # Find and click submit button - tìm nút ĐĂNG KÝ
                self.log("🔍 Đang tìm nút đăng ký...")
                submit_clicked = False

                # Wait a bit more for any dynamic content
                time.sleep(1)

                # Method 1: Find by exact text content
                exact_text_methods = [
                    "//button[text()='ĐĂNG KÝ']",
                    "//button[normalize-space(text())='ĐĂNG KÝ']",
                    "//input[@value='ĐĂNG KÝ']",
                    "//button[text()='Đăng ký']",
                    "//button[normalize-space(text())='Đăng ký']",
                    "//input[@value='Đăng ký']"
                ]

                for xpath in exact_text_methods:
                    try:
                        buttons = self.driver.find_elements(By.XPATH, xpath)
                        if buttons:
                            for button in buttons:
                                if button.is_displayed() and button.is_enabled():
                                    self.log(f"🎯 Tìm thấy nút (exact text): {button.text or button.get_attribute('value')}")
                                    self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                    time.sleep(0.5)

                                    try:
                                        button.click()
                                        self.log("✅ Đã click nút đăng ký!")
                                        submit_clicked = True
                                        break
                                    except:
                                        try:
                                            self.driver.execute_script("arguments[0].click();", button)
                                            self.log("✅ Đã click nút đăng ký (JavaScript)!")
                                            submit_clicked = True
                                            break
                                        except:
                                            continue
                        if submit_clicked:
                            break
                    except:
                        continue

                # Method 2: Find by contains text
                if not submit_clicked:
                    contains_text_methods = [
                        "//button[contains(text(), 'ĐĂNG KÝ')]",
                        "//button[contains(text(), 'Đăng ký')]",
                        "//button[contains(text(), 'REGISTER')]",
                        "//button[contains(text(), 'Register')]",
                        "//input[contains(@value, 'ĐĂNG KÝ')]",
                        "//input[contains(@value, 'Đăng ký')]"
                    ]

                    for xpath in contains_text_methods:
                        try:
                            buttons = self.driver.find_elements(By.XPATH, xpath)
                            if buttons:
                                for button in buttons:
                                    if button.is_displayed() and button.is_enabled():
                                        self.log(f"🎯 Tìm thấy nút (contains text): {button.text or button.get_attribute('value')}")
                                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                        time.sleep(0.5)

                                        try:
                                            button.click()
                                            self.log("✅ Đã click nút đăng ký!")
                                            submit_clicked = True
                                            break
                                        except:
                                            try:
                                                self.driver.execute_script("arguments[0].click();", button)
                                                self.log("✅ Đã click nút đăng ký (JavaScript)!")
                                                submit_clicked = True
                                                break
                                            except:
                                                continue
                            if submit_clicked:
                                break
                        except:
                            continue

                # Method 3: Find by button type and common CSS selectors
                if not submit_clicked:
                    css_selectors = [
                        "button[type='submit']",
                        "input[type='submit']",
                        "button.btn-primary",
                        "button.btn-submit",
                        "button.register-btn",
                        ".btn-register",
                        "button[style*='background-color']",
                        "button[style*='orange']",
                        "button[style*='#']"  # Any button with color styling
                    ]

                    for selector in css_selectors:
                        try:
                            buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            if buttons:
                                for button in buttons:
                                    if button.is_displayed() and button.is_enabled():
                                        button_text = button.text or button.get_attribute('value') or 'No text'
                                        self.log(f"🎯 Tìm thấy nút (CSS): {button_text}")
                                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                        time.sleep(0.5)

                                        try:
                                            button.click()
                                            self.log("✅ Đã click nút đăng ký!")
                                            submit_clicked = True
                                            break
                                        except:
                                            try:
                                                self.driver.execute_script("arguments[0].click();", button)
                                                self.log("✅ Đã click nút đăng ký (JavaScript)!")
                                                submit_clicked = True
                                                break
                                            except:
                                                continue
                            if submit_clicked:
                                break
                        except:
                            continue

                # Method 4: Find any button and check text content
                if not submit_clicked:
                    self.log("🔍 Tìm kiếm tất cả các nút trên trang...")
                    try:
                        all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                        all_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit'], input[type='button']")
                        all_clickable = all_buttons + all_inputs

                        self.log(f"📋 Tìm thấy {len(all_clickable)} nút có thể click")

                        for i, element in enumerate(all_clickable):
                            try:
                                if element.is_displayed() and element.is_enabled():
                                    element_text = element.text or element.get_attribute('value') or ''
                                    element_class = element.get_attribute('class') or ''
                                    element_id = element.get_attribute('id') or ''

                                    self.log(f"  Nút {i+1}: '{element_text}' | Class: {element_class} | ID: {element_id}")

                                    # Check if this looks like a submit button
                                    if any(keyword in element_text.upper() for keyword in ['ĐĂNG', 'REGISTER', 'SUBMIT']) or \
                                       any(keyword in element_class.lower() for keyword in ['submit', 'register', 'btn-primary']) or \
                                       any(keyword in element_id.lower() for keyword in ['submit', 'register']):

                                        self.log(f"🎯 Thử click nút: '{element_text}'")
                                        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                        time.sleep(0.5)

                                        try:
                                            element.click()
                                            self.log("✅ Đã click nút đăng ký!")
                                            submit_clicked = True
                                            break
                                        except:
                                            try:
                                                self.driver.execute_script("arguments[0].click();", element)
                                                self.log("✅ Đã click nút đăng ký (JavaScript)!")
                                                submit_clicked = True
                                                break
                                            except:
                                                continue
                            except:
                                continue
                    except Exception as e:
                        self.log(f"❌ Lỗi tìm nút: {str(e)[:50]}")

                if not submit_clicked:
                    self.log("⚠️ Không tìm thấy nút đăng ký, vui lòng click thủ công")
                    self.log("💡 Hãy tìm và click nút màu cam 'ĐĂNG KÝ' trên trang")
                    self.log("🔍 Sử dụng nút 'Debug Form' để xem chi tiết các nút trên trang")

                # Wait and check result
                time.sleep(5)

                # Update display with generated info
                if not self.username_var.get().strip():
                    self.username_var.set(username)
                if not self.password_var.get().strip():
                    self.password_var.set(password)

                self.log("🎉 Quá trình đăng ký hoàn thành!")
                self.log("💡 Kiểm tra kết quả trên trang web")

                # Ask if want to continue
                result = messagebox.askyesno(
                    "Đăng ký hoàn thành",
                    f"Đã hoàn thành đăng ký!\n\n"
                    f"Tài khoản: {username}\n"
                    f"Mật khẩu: {password}\n\n"
                    f"Bạn có muốn đăng ký tài khoản khác không?"
                )

                if result:
                    # Reset for next registration
                    self.username_var.set("")
                    self.password_var.set("")
                    self.register_btn.config(state=tk.NORMAL)
                    self.log("🔄 Sẵn sàng đăng ký tài khoản tiếp theo")
                else:
                    self.log("✅ Hoàn thành!")

            except Exception as e:
                self.log(f"❌ Lỗi đăng ký: {str(e)}")
                self.register_btn.config(state=tk.NORMAL)

        threading.Thread(target=register, daemon=True).start()

    def on_closing(self):
        """Xử lý khi đóng ứng dụng"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        self.root.destroy()


def main():
    """Main function"""
    root = tk.Tk()
    app = SimpleAutoRegister(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start GUI
    root.mainloop()


if __name__ == "__main__":
    main()
