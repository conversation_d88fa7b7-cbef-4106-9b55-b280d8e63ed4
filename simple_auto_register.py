#!/usr/bin/env python3
"""
Simple Auto Register Tool
Tool đơn giản tự động đăng ký tài kho<PERSON>n
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import string
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from simple_chrome_tool import download_chromedriver_137

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('auto_register.log', encoding='utf-8')]
)
logger = logging.getLogger(__name__)

class SimpleAutoRegister:
    def __init__(self, root):
        self.root = root
        self.root.title("Auto Register Tool - 13win")
        self.root.geometry("700x600")
        self.root.resizable(True, True)

        self.driver = None
        self.is_running = False

        self.create_widgets()

    def create_widgets(self):
        """Tạo giao diện đơn giản"""
        # Title
        title_label = ttk.Label(self.root, text="Auto Register Tool", font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # URL Section
        url_frame = ttk.LabelFrame(main_frame, text="Website", padding="10")
        url_frame.pack(fill=tk.X, pady=(0, 20))

        self.url_var = tk.StringVar(value="https://www.13win16.com/?id=*********")
        ttk.Label(url_frame, text="URL:").pack(anchor=tk.W)
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=60)
        url_entry.pack(fill=tk.X, pady=(5, 0))

        # Account Info Section
        info_frame = ttk.LabelFrame(main_frame, text="Thông tin đăng ký", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # Username
        ttk.Label(info_frame, text="Tài khoản (để trống = tự động):").pack(anchor=tk.W)
        self.username_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.username_var, width=30).pack(anchor=tk.W, pady=(5, 10))

        # Password
        ttk.Label(info_frame, text="Mật khẩu (để trống = tự động):").pack(anchor=tk.W)
        self.password_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.password_var, width=30, show="*").pack(anchor=tk.W, pady=(5, 10))

        # Full name
        ttk.Label(info_frame, text="Họ và tên:").pack(anchor=tk.W)
        self.fullname_var = tk.StringVar(value="TRAN HOANG AN")
        ttk.Entry(info_frame, textvariable=self.fullname_var, width=30).pack(anchor=tk.W, pady=(5, 10))

        # Bank name
        ttk.Label(info_frame, text="Tên ngân hàng:").pack(anchor=tk.W)
        self.bank_var = tk.StringVar(value="VIETCOMBANK")
        bank_combo = ttk.Combobox(info_frame, textvariable=self.bank_var, width=27)
        bank_combo['values'] = ['VIETCOMBANK', 'TECHCOMBANK', 'BIDV', 'AGRIBANK', 'SACOMBANK', 'MB', 'VPBank', 'ACB']
        bank_combo.pack(anchor=tk.W, pady=(5, 0))

        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        # Row 1: Main buttons
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 10))

        self.start_btn = ttk.Button(button_row1, text="🚀 Bước 1: Khởi động Chrome",
                                   command=self.start_chrome, width=25)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.navigate_btn = ttk.Button(button_row1, text="🌐 Bước 2: Truy cập 13win",
                                      command=self.navigate_website, state=tk.DISABLED, width=25)
        self.navigate_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Row 2: Register button
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        self.register_btn = ttk.Button(button_row2, text="🤖 Bước 3: Tự động Đăng ký",
                                      command=self.auto_register, state=tk.DISABLED, width=52)
        self.register_btn.pack(pady=5)

        # Status label
        self.status_label = ttk.Label(button_row2, text="Sẵn sàng bắt đầu...", foreground="blue")
        self.status_label.pack(pady=5)

        # Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # Log text with scrollbar
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_container, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def log(self, message):
        """Thêm message vào log"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # Update status label with latest message
        self.status_label.config(text=message[:50] + "..." if len(message) > 50 else message)

        logger.info(message)

    def generate_random_string(self, length=8):
        """Tạo chuỗi ngẫu nhiên"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    def start_chrome(self):
        """Bước 1: Khởi động Chrome"""
        def start():
            try:
                self.log("🚀 Đang khởi động Chrome...")
                self.start_btn.config(state=tk.DISABLED)

                # Setup Chrome options
                chrome_options = Options()
                chrome_options.add_argument("--window-size=1200,800")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # Get ChromeDriver
                driver_path = download_chromedriver_137()
                if not driver_path:
                    raise Exception("Không thể tải ChromeDriver")

                # Start Chrome
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # Hide automation indicators
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                self.is_running = True
                self.log("✅ Chrome đã khởi động thành công!")

                # Enable next button
                self.navigate_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log(f"❌ Lỗi khởi động Chrome: {str(e)}")
                self.start_btn.config(state=tk.NORMAL)

        threading.Thread(target=start, daemon=True).start()

    def navigate_website(self):
        """Bước 2: Truy cập website"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        def navigate():
            try:
                self.log("🌐 Đang truy cập website...")
                self.navigate_btn.config(state=tk.DISABLED)

                url = self.url_var.get().strip()
                if not url:
                    raise Exception("URL không được để trống!")

                # Try multiple URLs
                urls_to_try = [
                    url,
                    url.replace("13win16", "13win20"),
                    url.replace("13win16", "13win"),
                    url.replace(".com", ".net"),
                    "https://13win.com/?id=*********",
                    "https://13win20.com/?id=*********"
                ]

                success = False
                for test_url in urls_to_try:
                    try:
                        self.log(f"🔗 Thử truy cập: {test_url}")
                        self.driver.set_page_load_timeout(15)
                        self.driver.get(test_url)

                        # Wait for page load
                        time.sleep(3)

                        # Check if page loaded successfully
                        title = self.driver.title.lower()
                        current_url = self.driver.current_url.lower()

                        if any(keyword in title or keyword in current_url for keyword in ['13win', 'casino', 'game']):
                            self.log(f"✅ Truy cập thành công: {self.driver.current_url}")
                            self.log(f"📄 Tiêu đề: {self.driver.title}")
                            success = True
                            break

                    except Exception as e:
                        self.log(f"❌ Lỗi truy cập {test_url}: {str(e)[:50]}...")
                        continue

                if success:
                    # Enable register button
                    self.register_btn.config(state=tk.NORMAL)
                    self.log("✅ Sẵn sàng để đăng ký!")
                else:
                    self.log("❌ Không thể truy cập website")
                    self.navigate_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log(f"❌ Lỗi: {str(e)}")
                self.navigate_btn.config(state=tk.NORMAL)

        threading.Thread(target=navigate, daemon=True).start()

    def find_and_fill_field(self, field_selectors, value, field_name):
        """Tìm và điền field"""
        for selector in field_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.clear()
                        element.send_keys(value)
                        self.log(f"✅ Đã điền {field_name}: {value}")
                        time.sleep(0.5)
                        return True
            except Exception:
                continue

        self.log(f"⚠️ Không tìm thấy trường {field_name}")
        return False

    def auto_register(self):
        """Bước 3: Tự động đăng ký"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Vui lòng hoàn thành bước 1 và 2!")
            return

        def register():
            try:
                self.log("🤖 Bắt đầu tự động đăng ký...")
                self.register_btn.config(state=tk.DISABLED)

                # Generate account info
                username = self.username_var.get().strip() or f"user{random.randint(1000, 9999)}"
                password = self.password_var.get().strip() or self.generate_random_string(8)
                fullname = self.fullname_var.get().strip() or "TRAN HOANG AN"
                bank_name = self.bank_var.get().strip() or "VIETCOMBANK"

                self.log(f"📝 Thông tin đăng ký:")
                self.log(f"   👤 Tài khoản: {username}")
                self.log(f"   🔑 Mật khẩu: {password}")
                self.log(f"   👨 Họ tên: {fullname}")
                self.log(f"   🏦 Ngân hàng: {bank_name}")

                # Wait a bit for page to be ready
                time.sleep(2)

                # Fill username
                username_selectors = [
                    'input[name*="username"]', 'input[name*="user"]', 'input[name*="login"]',
                    'input[id*="username"]', 'input[id*="user"]', 'input[id*="login"]',
                    'input[placeholder*="tài khoản"]', 'input[placeholder*="username"]',
                    'input[type="text"]:not([name*="name"]):not([name*="bank"])'
                ]
                self.find_and_fill_field(username_selectors, username, "tài khoản")

                # Fill password
                password_selectors = [
                    'input[name*="password"]', 'input[name*="pass"]',
                    'input[id*="password"]', 'input[id*="pass"]',
                    'input[placeholder*="mật khẩu"]', 'input[placeholder*="password"]',
                    'input[type="password"]'
                ]
                password_fields = []
                for selector in password_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                password_fields.append(element)
                    except:
                        continue

                # Fill password and confirm password
                for i, field in enumerate(password_fields[:2]):  # Max 2 password fields
                    try:
                        field.clear()
                        field.send_keys(password)
                        field_type = "mật khẩu" if i == 0 else "nhập lại mật khẩu"
                        self.log(f"✅ Đã điền {field_type}")
                        time.sleep(0.5)
                    except:
                        continue

                # Fill fullname
                name_selectors = [
                    'input[name*="name"]:not([name*="username"]):not([name*="bank"])',
                    'input[name*="fullname"]', 'input[name*="full_name"]',
                    'input[id*="name"]:not([id*="username"]):not([id*="bank"])',
                    'input[placeholder*="họ tên"]', 'input[placeholder*="name"]'
                ]
                self.find_and_fill_field(name_selectors, fullname, "họ tên")

                # Fill bank name
                bank_selectors = [
                    'input[name*="bank"]', 'input[id*="bank"]',
                    'input[placeholder*="ngân hàng"]', 'input[placeholder*="bank"]',
                    'select[name*="bank"]', 'select[id*="bank"]'
                ]
                self.find_and_fill_field(bank_selectors, bank_name, "tên ngân hàng")

                # Wait before submit
                time.sleep(2)

                # Find and click submit button
                submit_selectors = [
                    'button[type="submit"]', 'input[type="submit"]',
                    'button:contains("Đăng ký")', 'button:contains("Register")',
                    'button:contains("Submit")', '.btn-submit', '.register-btn'
                ]

                submit_clicked = False
                for selector in submit_selectors:
                    try:
                        if ':contains(' in selector:
                            # Use XPath for text content
                            text = selector.split(':contains(')[1].split(')')[0].strip('"')
                            xpath = f"//button[contains(text(), '{text}')]"
                            buttons = self.driver.find_elements(By.XPATH, xpath)
                        else:
                            buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                        for button in buttons:
                            if button.is_displayed() and button.is_enabled():
                                # Scroll to button
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(1)

                                # Click button
                                try:
                                    button.click()
                                except:
                                    self.driver.execute_script("arguments[0].click();", button)

                                self.log("✅ Đã click nút đăng ký!")
                                submit_clicked = True
                                break

                        if submit_clicked:
                            break

                    except Exception:
                        continue

                if not submit_clicked:
                    self.log("⚠️ Không tìm thấy nút đăng ký, vui lòng click thủ công")

                # Wait and check result
                time.sleep(5)

                # Update display with generated info
                if not self.username_var.get().strip():
                    self.username_var.set(username)
                if not self.password_var.get().strip():
                    self.password_var.set(password)

                self.log("🎉 Quá trình đăng ký hoàn thành!")
                self.log("💡 Kiểm tra kết quả trên trang web")

                # Ask if want to continue
                result = messagebox.askyesno(
                    "Đăng ký hoàn thành",
                    f"Đã hoàn thành đăng ký!\n\n"
                    f"Tài khoản: {username}\n"
                    f"Mật khẩu: {password}\n\n"
                    f"Bạn có muốn đăng ký tài khoản khác không?"
                )

                if result:
                    # Reset for next registration
                    self.username_var.set("")
                    self.password_var.set("")
                    self.register_btn.config(state=tk.NORMAL)
                    self.log("🔄 Sẵn sàng đăng ký tài khoản tiếp theo")
                else:
                    self.log("✅ Hoàn thành!")

            except Exception as e:
                self.log(f"❌ Lỗi đăng ký: {str(e)}")
                self.register_btn.config(state=tk.NORMAL)

        threading.Thread(target=register, daemon=True).start()

    def on_closing(self):
        """Xử lý khi đóng ứng dụng"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        self.root.destroy()


def main():
    """Main function"""
    root = tk.Tk()
    app = SimpleAutoRegister(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start GUI
    root.mainloop()


if __name__ == "__main__":
    main()
