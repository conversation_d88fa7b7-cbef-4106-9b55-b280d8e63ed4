#!/usr/bin/env python3
"""
Simple Auto Register Tool
Tool đơn giản tự động đăng ký tài kho<PERSON>n
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import string
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from simple_chrome_tool import download_chromedriver_137

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('auto_register.log', encoding='utf-8')]
)
logger = logging.getLogger(__name__)

class SimpleAutoRegister:
    def __init__(self, root):
        self.root = root
        self.root.title("Auto Register Tool - 13win")
        self.root.geometry("700x600")
        self.root.resizable(True, True)

        self.driver = None
        self.is_running = False

        self.create_widgets()

    def create_widgets(self):
        """Tạo giao diện đơn giản"""
        # Title
        title_label = ttk.Label(self.root, text="Auto Register Tool", font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # URL Section
        url_frame = ttk.LabelFrame(main_frame, text="Website", padding="10")
        url_frame.pack(fill=tk.X, pady=(0, 20))

        self.url_var = tk.StringVar(value="https://www.13win16.com/?id=*********")
        ttk.Label(url_frame, text="URL:").pack(anchor=tk.W)
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=60)
        url_entry.pack(fill=tk.X, pady=(5, 0))

        # Account Info Section
        info_frame = ttk.LabelFrame(main_frame, text="Thông tin đăng ký", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # Username
        ttk.Label(info_frame, text="Tài khoản (để trống = tự động):").pack(anchor=tk.W)
        self.username_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.username_var, width=30).pack(anchor=tk.W, pady=(5, 10))

        # Password
        ttk.Label(info_frame, text="Mật khẩu (để trống = tự động):").pack(anchor=tk.W)
        self.password_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.password_var, width=30, show="*").pack(anchor=tk.W, pady=(5, 10))

        # Full name
        ttk.Label(info_frame, text="Họ và tên:").pack(anchor=tk.W)
        self.fullname_var = tk.StringVar(value="TRAN HOANG AN")
        ttk.Entry(info_frame, textvariable=self.fullname_var, width=30).pack(anchor=tk.W, pady=(5, 10))

        # Note
        note_label = ttk.Label(info_frame, text="💡 Form có 4 trường: Tài khoản, Mật khẩu, Nhập lại mật khẩu, Họ tên",
                              foreground="gray", font=("Arial", 9))
        note_label.pack(anchor=tk.W, pady=(5, 0))

        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        # Row 1: Main buttons
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 10))

        self.start_btn = ttk.Button(button_row1, text="🚀 Bước 1: Khởi động Chrome",
                                   command=self.start_chrome, width=25)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.navigate_btn = ttk.Button(button_row1, text="🌐 Bước 2: Truy cập 13win",
                                      command=self.navigate_website, state=tk.DISABLED, width=25)
        self.navigate_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.debug_btn = ttk.Button(button_row1, text="🔍 Debug Form",
                                   command=self.debug_form, state=tk.DISABLED, width=15)
        self.debug_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Row 2: Register button
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        self.register_btn = ttk.Button(button_row2, text="🤖 Bước 3: Tự động Đăng ký",
                                      command=self.auto_register, state=tk.DISABLED, width=52)
        self.register_btn.pack(pady=5)

        # Status label
        self.status_label = ttk.Label(button_row2, text="Sẵn sàng bắt đầu...", foreground="blue")
        self.status_label.pack(pady=5)

        # Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # Log text with scrollbar
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_container, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def log(self, message):
        """Thêm message vào log"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # Update status label with latest message
        self.status_label.config(text=message[:50] + "..." if len(message) > 50 else message)

        logger.info(message)

    def generate_random_string(self, length=8):
        """Tạo chuỗi ngẫu nhiên"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    def start_chrome(self):
        """Bước 1: Khởi động Chrome"""
        def start():
            try:
                self.log("🚀 Đang khởi động Chrome...")
                self.start_btn.config(state=tk.DISABLED)

                # Setup Chrome options
                chrome_options = Options()
                chrome_options.add_argument("--window-size=1200,800")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # Get ChromeDriver
                driver_path = download_chromedriver_137()
                if not driver_path:
                    raise Exception("Không thể tải ChromeDriver")

                # Start Chrome
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # Hide automation indicators
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                self.is_running = True
                self.log("✅ Chrome đã khởi động thành công!")

                # Enable next button
                self.navigate_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log(f"❌ Lỗi khởi động Chrome: {str(e)}")
                self.start_btn.config(state=tk.NORMAL)

        threading.Thread(target=start, daemon=True).start()

    def navigate_website(self):
        """Bước 2: Truy cập website"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        def navigate():
            try:
                self.log("🌐 Đang truy cập website...")
                self.navigate_btn.config(state=tk.DISABLED)

                url = self.url_var.get().strip()
                if not url:
                    raise Exception("URL không được để trống!")

                # Try multiple URLs
                urls_to_try = [
                    url,
                    url.replace("13win16", "13win20"),
                    url.replace("13win16", "13win"),
                    url.replace(".com", ".net"),
                    "https://13win.com/?id=*********",
                    "https://13win20.com/?id=*********"
                ]

                success = False
                for test_url in urls_to_try:
                    try:
                        self.log(f"🔗 Thử truy cập: {test_url}")
                        self.driver.set_page_load_timeout(15)
                        self.driver.get(test_url)

                        # Wait for page load
                        time.sleep(3)

                        # Check if page loaded successfully
                        title = self.driver.title.lower()
                        current_url = self.driver.current_url.lower()

                        if any(keyword in title or keyword in current_url for keyword in ['13win', 'casino', 'game']):
                            self.log(f"✅ Truy cập thành công: {self.driver.current_url}")
                            self.log(f"📄 Tiêu đề: {self.driver.title}")
                            success = True
                            break

                    except Exception as e:
                        self.log(f"❌ Lỗi truy cập {test_url}: {str(e)[:50]}...")
                        continue

                if success:
                    # Enable register button and debug button
                    self.register_btn.config(state=tk.NORMAL)
                    self.debug_btn.config(state=tk.NORMAL)
                    self.log("✅ Sẵn sàng để đăng ký!")
                else:
                    self.log("❌ Không thể truy cập website")
                    self.navigate_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.log(f"❌ Lỗi: {str(e)}")
                self.navigate_btn.config(state=tk.NORMAL)

        threading.Thread(target=navigate, daemon=True).start()

    def debug_form(self):
        """Debug: Hiển thị tất cả form elements trên trang"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        def debug():
            try:
                self.log("🔍 Đang phân tích form...")

                # Find all input elements
                inputs = self.driver.find_elements(By.TAG_NAME, "input")
                self.log(f"📋 Tìm thấy {len(inputs)} input elements:")

                for i, input_elem in enumerate(inputs):
                    try:
                        if input_elem.is_displayed():
                            input_type = input_elem.get_attribute('type') or 'text'
                            input_name = input_elem.get_attribute('name') or 'N/A'
                            input_id = input_elem.get_attribute('id') or 'N/A'
                            input_placeholder = input_elem.get_attribute('placeholder') or 'N/A'
                            input_value = input_elem.get_attribute('value') or 'Empty'

                            self.log(f"  {i+1}. Type: {input_type} | Name: {input_name} | ID: {input_id}")
                            self.log(f"      Placeholder: {input_placeholder} | Value: {input_value}")
                    except:
                        continue

                # Find all buttons
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                self.log(f"🔘 Tìm thấy {len(buttons)} button elements:")

                for i, button in enumerate(buttons):
                    try:
                        if button.is_displayed():
                            button_text = button.text or 'No text'
                            button_type = button.get_attribute('type') or 'button'
                            button_class = button.get_attribute('class') or 'N/A'

                            self.log(f"  {i+1}. Text: '{button_text}' | Type: {button_type} | Class: {button_class}")
                    except:
                        continue

                # Find all select elements
                selects = self.driver.find_elements(By.TAG_NAME, "select")
                if selects:
                    self.log(f"📝 Tìm thấy {len(selects)} select elements:")
                    for i, select in enumerate(selects):
                        try:
                            if select.is_displayed():
                                select_name = select.get_attribute('name') or 'N/A'
                                select_id = select.get_attribute('id') or 'N/A'
                                self.log(f"  {i+1}. Name: {select_name} | ID: {select_id}")
                        except:
                            continue

                self.log("✅ Debug hoàn thành!")

            except Exception as e:
                self.log(f"❌ Lỗi debug: {str(e)}")

        threading.Thread(target=debug, daemon=True).start()

    def find_and_fill_field(self, field_selectors, value, field_name, clear_first=True):
        """Tìm và điền field"""
        for selector in field_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        if clear_first:
                            element.clear()
                        element.send_keys(value)
                        self.log(f"✅ Đã điền {field_name}: {value}")
                        time.sleep(0.5)
                        return True
            except Exception:
                continue

        # Try XPath selectors
        xpath_selectors = [
            f"//input[@placeholder[contains(., '{field_name.lower()}')]]",
            f"//input[@name[contains(., '{field_name.lower()}')]]",
            f"//input[@id[contains(., '{field_name.lower()}')]]"
        ]

        for xpath in xpath_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        if clear_first:
                            element.clear()
                        element.send_keys(value)
                        self.log(f"✅ Đã điền {field_name}: {value}")
                        time.sleep(0.5)
                        return True
            except Exception:
                continue

        self.log(f"⚠️ Không tìm thấy trường {field_name}")
        return False

    def auto_register(self):
        """Bước 3: Tự động đăng ký"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Vui lòng hoàn thành bước 1 và 2!")
            return

        def register():
            try:
                self.log("🤖 Bắt đầu tự động đăng ký...")
                self.register_btn.config(state=tk.DISABLED)

                # Generate account info
                username = self.username_var.get().strip() or f"user{random.randint(1000, 9999)}"
                password = self.password_var.get().strip() or self.generate_random_string(8)
                fullname = self.fullname_var.get().strip() or "TRAN HOANG AN"

                self.log(f"📝 Thông tin đăng ký:")
                self.log(f"   👤 Tài khoản: {username}")
                self.log(f"   🔑 Mật khẩu: {password}")
                self.log(f"   👨 Họ tên: {fullname}")

                # Wait a bit for page to be ready
                time.sleep(3)

                # Check current username field value
                try:
                    username_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                    current_value = username_field.get_attribute('value')
                    if current_value and current_value.startswith('user'):
                        self.log(f"📱 Tài khoản hiện tại: {current_value}")
                        username = current_value  # Use existing username
                    else:
                        # Clear and fill new username
                        username_field.clear()
                        username_field.send_keys(username)
                        self.log(f"✅ Đã điền tài khoản: {username}")
                except Exception as e:
                    self.log(f"⚠️ Không thể kiểm tra trường tài khoản: {str(e)[:50]}")

                time.sleep(1)

                # Fill password - tìm trường mật khẩu
                password_filled = False
                password_selectors = [
                    'input[type="password"]',
                    'input[placeholder*="Mật khẩu"]',
                    'input[placeholder*="mật khẩu"]',
                    'input[name*="password"]',
                    'input[id*="password"]'
                ]

                for selector in password_selectors:
                    try:
                        password_fields = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for field in password_fields:
                            if field.is_displayed() and field.is_enabled():
                                field.clear()
                                field.send_keys(password)
                                self.log(f"✅ Đã điền mật khẩu")
                                password_filled = True
                                time.sleep(1)
                                break
                        if password_filled:
                            break
                    except Exception:
                        continue

                if not password_filled:
                    self.log("⚠️ Không tìm thấy trường mật khẩu")

                # Fill fullname - tìm trường họ tên
                fullname_selectors = [
                    'input[placeholder*="Họ Tên"]',
                    'input[placeholder*="họ tên"]',
                    'input[placeholder*="Họ tên"]',
                    'input[name*="fullname"]',
                    'input[name*="name"]:not([name*="username"])',
                    'input[id*="fullname"]'
                ]

                fullname_filled = False
                for selector in fullname_selectors:
                    try:
                        name_fields = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for field in name_fields:
                            if field.is_displayed() and field.is_enabled():
                                field.clear()
                                field.send_keys(fullname)
                                self.log(f"✅ Đã điền họ tên: {fullname}")
                                fullname_filled = True
                                time.sleep(1)
                                break
                        if fullname_filled:
                            break
                    except Exception:
                        continue

                if not fullname_filled:
                    self.log("⚠️ Không tìm thấy trường họ tên")

                # Wait before submit
                time.sleep(2)

                # Find and click submit button - tìm nút ĐĂNG KÝ
                submit_clicked = False

                # Try different ways to find submit button
                submit_methods = [
                    # Method 1: By button text
                    ("xpath", "//button[contains(text(), 'ĐĂNG KÝ')]"),
                    ("xpath", "//button[contains(text(), 'Đăng ký')]"),
                    ("xpath", "//button[contains(text(), 'Register')]"),
                    ("xpath", "//input[@value='ĐĂNG KÝ']"),
                    ("xpath", "//input[@value='Đăng ký']"),

                    # Method 2: By button type
                    ("css", "button[type='submit']"),
                    ("css", "input[type='submit']"),

                    # Method 3: By class or common patterns
                    ("css", ".btn-primary"),
                    ("css", ".btn-submit"),
                    ("css", ".register-btn"),
                    ("css", "button.btn"),

                    # Method 4: Orange button (from image)
                    ("css", "button[style*='background']"),
                    ("css", "button[style*='orange']"),
                ]

                for method, selector in submit_methods:
                    try:
                        if method == "xpath":
                            buttons = self.driver.find_elements(By.XPATH, selector)
                        else:
                            buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                        for button in buttons:
                            if button.is_displayed() and button.is_enabled():
                                # Scroll to button
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(1)

                                # Get button text for confirmation
                                button_text = button.text or button.get_attribute('value') or 'Unknown'
                                self.log(f"🎯 Tìm thấy nút: {button_text}")

                                # Click button
                                try:
                                    button.click()
                                    self.log("✅ Đã click nút đăng ký!")
                                except:
                                    self.driver.execute_script("arguments[0].click();", button)
                                    self.log("✅ Đã click nút đăng ký (JavaScript)!")

                                submit_clicked = True
                                break

                        if submit_clicked:
                            break

                    except Exception as e:
                        continue

                if not submit_clicked:
                    # Last resort: try to find any clickable button
                    try:
                        all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                        for button in all_buttons:
                            if button.is_displayed() and button.is_enabled():
                                button_text = button.text.strip()
                                if any(keyword in button_text.upper() for keyword in ['ĐĂNG', 'REGISTER', 'SUBMIT']):
                                    self.log(f"🎯 Thử click nút: {button_text}")
                                    try:
                                        button.click()
                                        self.log("✅ Đã click nút đăng ký!")
                                        submit_clicked = True
                                        break
                                    except:
                                        continue
                    except:
                        pass

                if not submit_clicked:
                    self.log("⚠️ Không tìm thấy nút đăng ký, vui lòng click thủ công")
                    self.log("💡 Hãy tìm và click nút màu cam 'ĐĂNG KÝ' trên trang")

                # Wait and check result
                time.sleep(5)

                # Update display with generated info
                if not self.username_var.get().strip():
                    self.username_var.set(username)
                if not self.password_var.get().strip():
                    self.password_var.set(password)

                self.log("🎉 Quá trình đăng ký hoàn thành!")
                self.log("💡 Kiểm tra kết quả trên trang web")

                # Ask if want to continue
                result = messagebox.askyesno(
                    "Đăng ký hoàn thành",
                    f"Đã hoàn thành đăng ký!\n\n"
                    f"Tài khoản: {username}\n"
                    f"Mật khẩu: {password}\n\n"
                    f"Bạn có muốn đăng ký tài khoản khác không?"
                )

                if result:
                    # Reset for next registration
                    self.username_var.set("")
                    self.password_var.set("")
                    self.register_btn.config(state=tk.NORMAL)
                    self.log("🔄 Sẵn sàng đăng ký tài khoản tiếp theo")
                else:
                    self.log("✅ Hoàn thành!")

            except Exception as e:
                self.log(f"❌ Lỗi đăng ký: {str(e)}")
                self.register_btn.config(state=tk.NORMAL)

        threading.Thread(target=register, daemon=True).start()

    def on_closing(self):
        """Xử lý khi đóng ứng dụng"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        self.root.destroy()


def main():
    """Main function"""
    root = tk.Tk()
    app = SimpleAutoRegister(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start GUI
    root.mainloop()


if __name__ == "__main__":
    main()
