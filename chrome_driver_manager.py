#!/usr/bin/env python3
"""
Manual ChromeDriver manager for Windows compatibility
"""

import os
import sys
import zipfile
import requests
import subprocess
import json
from pathlib import Path

class ChromeDriverManager:
    def __init__(self):
        self.driver_dir = Path.home() / '.chrome_driver'
        self.driver_dir.mkdir(exist_ok=True)
        
    def get_chrome_version(self):
        """Get installed Chrome version"""
        try:
            if sys.platform == "win32":
                # Windows Chrome version detection
                import winreg
                try:
                    # Try Chrome registry key
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                    version, _ = winreg.QueryValueEx(key, "version")
                    winreg.CloseKey(key)
                    return version
                except:
                    # Alternative method using Chrome executable
                    chrome_paths = [
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
                    ]
                    
                    for chrome_path in chrome_paths:
                        if os.path.exists(chrome_path):
                            result = subprocess.run([chrome_path, "--version"], 
                                                  capture_output=True, text=True)
                            if result.returncode == 0:
                                version = result.stdout.strip().split()[-1]
                                return version
            else:
                # Linux/Mac Chrome version detection
                result = subprocess.run(["google-chrome", "--version"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    return version
                    
        except Exception as e:
            print(f"Could not detect Chrome version: {e}")
            
        return None
    
    def get_compatible_driver_version(self, chrome_version):
        """Get compatible ChromeDriver version"""
        try:
            major_version = chrome_version.split('.')[0]
            
            # ChromeDriver version API
            url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                return response.text.strip()
            else:
                # Fallback: try to get latest version
                url = "https://chromedriver.storage.googleapis.com/LATEST_RELEASE"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    return response.text.strip()
                    
        except Exception as e:
            print(f"Could not get ChromeDriver version: {e}")
            
        return None
    
    def download_chromedriver(self, version):
        """Download ChromeDriver"""
        try:
            # Determine platform
            if sys.platform == "win32":
                platform = "win32"
                executable_name = "chromedriver.exe"
            elif sys.platform == "darwin":
                platform = "mac64"
                executable_name = "chromedriver"
            else:
                platform = "linux64"
                executable_name = "chromedriver"
            
            # Download URL
            url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_{platform}.zip"
            
            print(f"Downloading ChromeDriver {version} for {platform}...")
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Save and extract
            zip_path = self.driver_dir / f"chromedriver_{version}.zip"
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # Extract
            extract_dir = self.driver_dir / version
            extract_dir.mkdir(exist_ok=True)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Find the executable
            driver_path = extract_dir / executable_name
            if not driver_path.exists():
                # Look in subdirectories
                for item in extract_dir.rglob(executable_name):
                    driver_path = item
                    break
            
            if driver_path.exists():
                # Make executable on Unix systems
                if sys.platform != "win32":
                    os.chmod(driver_path, 0o755)
                
                # Clean up zip file
                zip_path.unlink()
                
                print(f"ChromeDriver downloaded to: {driver_path}")
                return str(driver_path)
            else:
                raise Exception(f"Could not find {executable_name} in extracted files")
                
        except Exception as e:
            print(f"Failed to download ChromeDriver: {e}")
            return None
    
    def get_driver_path(self):
        """Get ChromeDriver path, download if necessary"""
        try:
            # Get Chrome version
            chrome_version = self.get_chrome_version()
            if not chrome_version:
                print("Could not detect Chrome version")
                return None
            
            print(f"Detected Chrome version: {chrome_version}")
            
            # Get compatible driver version
            driver_version = self.get_compatible_driver_version(chrome_version)
            if not driver_version:
                print("Could not determine compatible ChromeDriver version")
                return None
            
            print(f"Compatible ChromeDriver version: {driver_version}")
            
            # Check if already downloaded
            version_dir = self.driver_dir / driver_version
            executable_name = "chromedriver.exe" if sys.platform == "win32" else "chromedriver"
            
            # Look for existing driver
            for driver_path in version_dir.rglob(executable_name):
                if driver_path.is_file():
                    print(f"Using existing ChromeDriver: {driver_path}")
                    return str(driver_path)
            
            # Download if not found
            return self.download_chromedriver(driver_version)
            
        except Exception as e:
            print(f"Error getting ChromeDriver: {e}")
            return None


def main():
    """Test the ChromeDriver manager"""
    manager = ChromeDriverManager()
    driver_path = manager.get_driver_path()
    
    if driver_path:
        print(f"ChromeDriver ready at: {driver_path}")
    else:
        print("Failed to get ChromeDriver")


if __name__ == "__main__":
    main()
