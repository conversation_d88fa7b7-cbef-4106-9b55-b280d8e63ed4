#!/usr/bin/env python3
"""
Free Proxy Manager - <PERSON><PERSON><PERSON> danh s<PERSON>ch proxy miễn phí
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import threading
import logging

logger = logging.getLogger(__name__)

class ProxyManager:
    def __init__(self):
        self.proxies = []
        self.working_proxies = []
        self.proxy_sources = [
            'https://www.proxy-list.download/api/v1/get?type=http',
            'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all',
            'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
            'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
        ]
    
    def fetch_proxies_from_url(self, url):
        """Lấy proxy từ một URL"""
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                proxies = []
                lines = response.text.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if ':' in line and len(line.split(':')) == 2:
                        ip, port = line.split(':')
                        if self.is_valid_ip(ip) and port.isdigit():
                            proxies.append(f"{ip}:{port}")
                return proxies
        except Exception as e:
            logger.error(f"Lỗi khi lấy proxy từ {url}: {e}")
        return []
    
    def is_valid_ip(self, ip):
        """Kiểm tra IP hợp lệ"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False
    
    def fetch_all_proxies(self):
        """Lấy tất cả proxy từ các nguồn"""
        logger.info("Đang lấy danh sách proxy miễn phí...")
        all_proxies = []
        
        for url in self.proxy_sources:
            try:
                proxies = self.fetch_proxies_from_url(url)
                all_proxies.extend(proxies)
                logger.info(f"Lấy được {len(proxies)} proxy từ {url}")
                time.sleep(1)  # Tránh spam
            except Exception as e:
                logger.error(f"Lỗi khi lấy proxy từ {url}: {e}")
        
        # Loại bỏ trùng lặp
        self.proxies = list(set(all_proxies))
        logger.info(f"Tổng cộng có {len(self.proxies)} proxy duy nhất")
        return self.proxies
    
    def test_proxy(self, proxy, timeout=5):
        """Kiểm tra proxy có hoạt động không"""
        try:
            proxy_dict = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=timeout
            )
            
            if response.status_code == 200:
                return True
        except:
            pass
        return False
    
    def test_proxies_batch(self, proxies, max_workers=50):
        """Kiểm tra nhiều proxy cùng lúc"""
        working = []
        
        def test_single(proxy):
            if self.test_proxy(proxy):
                working.append(proxy)
                logger.info(f"Proxy hoạt động: {proxy}")
        
        threads = []
        for proxy in proxies:
            if len(threads) >= max_workers:
                # Chờ một số thread hoàn thành
                for t in threads[:10]:
                    t.join()
                threads = threads[10:]
            
            thread = threading.Thread(target=test_single, args=(proxy,))
            thread.start()
            threads.append(thread)
        
        # Chờ tất cả thread hoàn thành
        for thread in threads:
            thread.join()
        
        return working
    
    def get_working_proxies(self, limit=20):
        """Lấy danh sách proxy hoạt động"""
        if not self.proxies:
            self.fetch_all_proxies()
        
        if not self.proxies:
            logger.error("Không tìm thấy proxy nào")
            return []
        
        logger.info(f"Đang kiểm tra {len(self.proxies)} proxy...")
        
        # Trộn ngẫu nhiên và lấy một phần để test
        test_proxies = random.sample(self.proxies, min(100, len(self.proxies)))
        
        working = self.test_proxies_batch(test_proxies)
        self.working_proxies = working[:limit]
        
        logger.info(f"Tìm thấy {len(self.working_proxies)} proxy hoạt động")
        return self.working_proxies
    
    def get_random_proxy(self):
        """Lấy một proxy ngẫu nhiên"""
        if not self.working_proxies:
            self.get_working_proxies()
        
        if self.working_proxies:
            return random.choice(self.working_proxies)
        return None
    
    def get_proxy_info(self, proxy):
        """Lấy thông tin về proxy"""
        try:
            proxy_dict = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'proxy': proxy,
                    'ip': data.get('origin', 'Unknown'),
                    'status': 'Working'
                }
        except Exception as e:
            return {
                'proxy': proxy,
                'ip': 'Unknown',
                'status': f'Error: {str(e)}'
            }
        
        return {
            'proxy': proxy,
            'ip': 'Unknown',
            'status': 'Not working'
        }


def main():
    """Test proxy manager"""
    logging.basicConfig(level=logging.INFO)
    
    manager = ProxyManager()
    
    print("Đang lấy danh sách proxy...")
    proxies = manager.get_working_proxies(10)
    
    if proxies:
        print(f"\nTìm thấy {len(proxies)} proxy hoạt động:")
        for i, proxy in enumerate(proxies, 1):
            info = manager.get_proxy_info(proxy)
            print(f"{i}. {proxy} - IP: {info['ip']} - Status: {info['status']}")
    else:
        print("Không tìm thấy proxy nào hoạt động")


if __name__ == "__main__":
    main()
